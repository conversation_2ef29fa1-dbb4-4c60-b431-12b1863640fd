import { ContainerLayout } from '@/components/container-layout/container-layout'
import { GoBackToTop } from '@/components/goback-to-top'
import { NoteInfo } from '@/features/note/note-info'

interface NoteDetailProps {
  params: {
    id: string
  }
}

export default function NoteDetail({ params }: NoteDetailProps) {
  return (
    <ContainerLayout>
      <NoteInfo slug={params.id} />

      <GoBackToTop />
    </ContainerLayout>
  )
}
