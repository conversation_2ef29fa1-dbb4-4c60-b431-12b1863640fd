import { NextRequest, NextResponse } from 'next/server'
import { getAllPosts, getPublishedPosts, createPost, generateSlug, calculateReadTime } from '@/lib/database/posts'

// GET - 获取文章列表
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const includeUnpublished = searchParams.get('includeUnpublished') === 'true'
    const search = searchParams.get('search')
    const tags = searchParams.get('tags')?.split(',').filter(Boolean)

    let posts
    if (includeUnpublished) {
      posts = await getAllPosts()
    } else {
      posts = await getPublishedPosts()
    }

    // 搜索过滤
    if (search) {
      const searchTerm = search.toLowerCase()
      posts = posts.filter(post => 
        post.title.toLowerCase().includes(searchTerm) ||
        post.description?.toLowerCase().includes(searchTerm) ||
        post.content.toLowerCase().includes(searchTerm)
      )
    }

    // 标签过滤
    if (tags && tags.length > 0) {
      posts = posts.filter(post =>
        tags.some(tag => post.tags.some(postTag => postTag.name === tag))
      )
    }

    return NextResponse.json({
      success: true,
      data: posts
    })
  } catch (error) {
    console.error('Error in GET /api/posts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch posts' },
      { status: 500 }
    )
  }
}

// POST - 创建新文章
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { title, content, description, author, publish_status, tagIds } = body

    if (!title || typeof title !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Title is required' },
        { status: 400 }
      )
    }

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Content is required' },
        { status: 400 }
      )
    }

    // 生成slug和计算阅读时间
    const slug = generateSlug(title)
    const readTime = calculateReadTime(content)

    const postData = {
      title: title.trim(),
      content,
      description: description || null,
      author: author || 'TheoZhang',
      publish_status: publish_status || 'draft',
      slug,
      read_time: readTime,
      published_at: publish_status === 'published' ? new Date().toISOString() : null
    }

    const post = await createPost(postData, tagIds || [])

    return NextResponse.json({
      success: true,
      data: post
    })
  } catch (error) {
    console.error('Error in POST /api/posts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create post' },
      { status: 500 }
    )
  }
}
