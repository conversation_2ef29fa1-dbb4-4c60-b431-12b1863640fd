import { NextRequest, NextResponse } from 'next/server'
import { updatePost, deletePost, calculateReadTime } from '@/lib/database/posts'
import { supabaseAdmin } from '@/lib/supabase'

// GET - 获取单个文章
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      )
    }

    const { data, error } = await supabaseAdmin
      .from('posts')
      .select(`
        *,
        post_tags (
          tags (
            id,
            name,
            light_icon,
            dark_icon
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Post not found' },
          { status: 404 }
        )
      }
      throw error
    }

    const post = {
      ...data,
      tags: data.post_tags?.map(pt => pt.tags).filter(Boolean) || []
    }

    return NextResponse.json({
      success: true,
      data: post
    })
  } catch (error) {
    console.error('Error in GET /api/posts/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch post' },
      { status: 500 }
    )
  }
}

// PUT - 更新文章
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      )
    }

    const body = await req.json()
    const { title, content, description, author, publish_status, tagIds } = body

    const updateData: any = {}
    
    if (title) updateData.title = title.trim()
    if (content) {
      updateData.content = content
      updateData.read_time = calculateReadTime(content)
    }
    if (description !== undefined) updateData.description = description
    if (author) updateData.author = author
    if (publish_status) {
      updateData.publish_status = publish_status
      if (publish_status === 'published' && !updateData.published_at) {
        updateData.published_at = new Date().toISOString()
      }
    }

    const post = await updatePost(id, updateData, tagIds)

    return NextResponse.json({
      success: true,
      data: post
    })
  } catch (error) {
    console.error('Error in PUT /api/posts/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update post' },
      { status: 500 }
    )
  }
}

// DELETE - 删除文章
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      )
    }

    await deletePost(id)

    return NextResponse.json({
      success: true,
      message: 'Post deleted successfully'
    })
  } catch (error) {
    console.error('Error in DELETE /api/posts/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete post' },
      { status: 500 }
    )
  }
}
