import { NextRequest, NextResponse } from 'next/server'
import { aiSummaryService } from '@/utils/ai-config'

// POST 请求处理 - 生成 AI 总结
export async function POST(req: NextRequest) {
  try {
    const { content } = await req.json()

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { success: false, error: '内容不能为空' },
        { status: 400 }
      )
    }

    // 调用 AI 总结服务
    const result = await aiSummaryService.generateSummary(content)

    if (result.success) {
      return NextResponse.json({
        success: true,
        summary: result.summary
      })
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('AI 总结 API 错误:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// GET 请求处理 - 获取 AI 配置状态
export async function GET() {
  try {
    // 这里可以返回配置状态信息（不包含敏感信息如 API Key）
    return NextResponse.json({
      success: true,
      configured: true, // 这里可以检查是否已配置
      message: 'AI 总结服务可用'
    })
  } catch (error) {
    console.error('获取 AI 配置状态错误:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
