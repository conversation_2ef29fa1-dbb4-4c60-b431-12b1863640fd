import { NextRequest, NextResponse } from 'next/server'
import { getAllTags, createTag, deleteTags, searchTags, getTagsWithPostCount } from '@/lib/database/tags'

// GET - 获取所有标签或搜索标签
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const search = searchParams.get('search')
    const withCount = searchParams.get('withCount') === 'true'

    let tags
    if (search) {
      tags = await searchTags(search)
    } else if (withCount) {
      tags = await getTagsWithPostCount()
    } else {
      tags = await getAllTags()
    }

    return NextResponse.json({
      success: true,
      data: tags
    })
  } catch (error) {
    console.error('Error in GET /api/tags:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tags' },
      { status: 500 }
    )
  }
}

// POST - 创建新标签
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { name, light_icon, dark_icon } = body

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Tag name is required' },
        { status: 400 }
      )
    }

    const tag = await createTag({
      name: name.trim(),
      light_icon: light_icon || null,
      dark_icon: dark_icon || null
    })

    return NextResponse.json({
      success: true,
      data: tag
    })
  } catch (error) {
    console.error('Error in POST /api/tags:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create tag' },
      { status: 500 }
    )
  }
}

// DELETE - 删除多个标签
export async function DELETE(req: NextRequest) {
  try {
    const body = await req.json()
    const { ids } = body

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Tag IDs are required' },
        { status: 400 }
      )
    }

    await deleteTags(ids)

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${ids.length} tag(s)`
    })
  } catch (error) {
    console.error('Error in DELETE /api/tags:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete tags' },
      { status: 500 }
    )
  }
}
