import { NextRequest, NextResponse } from 'next/server'
import { getTagById, updateTag, deleteTag } from '@/lib/database/tags'

// GET - 获取单个标签
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid tag ID' },
        { status: 400 }
      )
    }

    const tag = await getTagById(id)
    
    if (!tag) {
      return NextResponse.json(
        { success: false, error: 'Tag not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: tag
    })
  } catch (error) {
    console.error('Error in GET /api/tags/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tag' },
      { status: 500 }
    )
  }
}

// PUT - 更新标签
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid tag ID' },
        { status: 400 }
      )
    }

    const body = await req.json()
    const { name, light_icon, dark_icon } = body

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Tag name is required' },
        { status: 400 }
      )
    }

    const tag = await updateTag(id, {
      name: name.trim(),
      light_icon: light_icon || null,
      dark_icon: dark_icon || null
    })

    return NextResponse.json({
      success: true,
      data: tag
    })
  } catch (error) {
    console.error('Error in PUT /api/tags/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update tag' },
      { status: 500 }
    )
  }
}

// DELETE - 删除单个标签
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid tag ID' },
        { status: 400 }
      )
    }

    await deleteTag(id)

    return NextResponse.json({
      success: true,
      message: 'Tag deleted successfully'
    })
  } catch (error) {
    console.error('Error in DELETE /api/tags/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete tag' },
      { status: 500 }
    )
  }
}
