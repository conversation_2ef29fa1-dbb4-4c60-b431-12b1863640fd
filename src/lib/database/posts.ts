import { supabase, supabaseAdmin } from '@/lib/supabase'
import { Database } from '@/types/database'

type Post = Database['public']['Tables']['posts']['Row']
type PostInsert = Database['public']['Tables']['posts']['Insert']
type PostUpdate = Database['public']['Tables']['posts']['Update']

export interface PostWithTags extends Post {
  tags: Array<{
    id: number
    name: string
    light_icon: string | null
    dark_icon: string | null
  }>
}

// Get all published posts with tags
export async function getPublishedPosts(): Promise<PostWithTags[]> {
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      post_tags (
        tags (
          id,
          name,
          light_icon,
          dark_icon
        )
      )
    `)
    .eq('publish_status', 'published')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching published posts:', error)
    throw new Error('Failed to fetch published posts')
  }

  return data.map(post => ({
    ...post,
    tags: post.post_tags?.map(pt => pt.tags).filter(Boolean) || []
  })) as PostWithTags[]
}

// Get all posts (for admin)
export async function getAllPosts(): Promise<PostWithTags[]> {
  const { data, error } = await supabaseAdmin
    .from('posts')
    .select(`
      *,
      post_tags (
        tags (
          id,
          name,
          light_icon,
          dark_icon
        )
      )
    `)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching all posts:', error)
    throw new Error('Failed to fetch posts')
  }

  return data.map(post => ({
    ...post,
    tags: post.post_tags?.map(pt => pt.tags).filter(Boolean) || []
  })) as PostWithTags[]
}

// Get post by slug
export async function getPostBySlug(slug: string): Promise<PostWithTags | null> {
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      post_tags (
        tags (
          id,
          name,
          light_icon,
          dark_icon
        )
      )
    `)
    .eq('slug', slug)
    .eq('publish_status', 'published')
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return null // Post not found
    }
    console.error('Error fetching post by slug:', error)
    throw new Error('Failed to fetch post')
  }

  return {
    ...data,
    tags: data.post_tags?.map(pt => pt.tags).filter(Boolean) || []
  } as PostWithTags
}

// Create new post
export async function createPost(post: PostInsert, tagIds: number[] = []): Promise<Post> {
  const { data, error } = await supabaseAdmin
    .from('posts')
    .insert(post)
    .select()
    .single()

  if (error) {
    console.error('Error creating post:', error)
    throw new Error('Failed to create post')
  }

  // Add tags if provided
  if (tagIds.length > 0) {
    await addTagsToPost(data.id, tagIds)
  }

  return data
}

// Update post
export async function updatePost(id: number, post: PostUpdate, tagIds?: number[]): Promise<Post> {
  const { data, error } = await supabaseAdmin
    .from('posts')
    .update(post)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating post:', error)
    throw new Error('Failed to update post')
  }

  // Update tags if provided
  if (tagIds !== undefined) {
    await removeAllTagsFromPost(id)
    if (tagIds.length > 0) {
      await addTagsToPost(id, tagIds)
    }
  }

  return data
}

// Delete post
export async function deletePost(id: number): Promise<void> {
  const { error } = await supabaseAdmin
    .from('posts')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting post:', error)
    throw new Error('Failed to delete post')
  }
}

// Add tags to post
export async function addTagsToPost(postId: number, tagIds: number[]): Promise<void> {
  const postTags = tagIds.map(tagId => ({ post_id: postId, tag_id: tagId }))
  
  const { error } = await supabaseAdmin
    .from('post_tags')
    .insert(postTags)

  if (error) {
    console.error('Error adding tags to post:', error)
    throw new Error('Failed to add tags to post')
  }
}

// Remove all tags from post
export async function removeAllTagsFromPost(postId: number): Promise<void> {
  const { error } = await supabaseAdmin
    .from('post_tags')
    .delete()
    .eq('post_id', postId)

  if (error) {
    console.error('Error removing tags from post:', error)
    throw new Error('Failed to remove tags from post')
  }
}

// Generate slug from title
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

// Calculate read time (words per minute)
export function calculateReadTime(content: string, wordsPerMinute: number = 200): number {
  const words = content.trim().split(/\s+/).length
  return Math.ceil(words / wordsPerMinute)
}
