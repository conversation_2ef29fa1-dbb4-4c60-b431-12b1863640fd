import { supabase, supabaseAdmin } from '@/lib/supabase'
import { Database } from '@/types/database'

type Tag = Database['public']['Tables']['tags']['Row']
type TagInsert = Database['public']['Tables']['tags']['Insert']
type TagUpdate = Database['public']['Tables']['tags']['Update']

// Get all tags
export async function getAllTags(): Promise<Tag[]> {
  const { data, error } = await supabase
    .from('tags')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching tags:', error)
    throw new Error('Failed to fetch tags')
  }

  return data
}

// Get tag by ID
export async function getTagById(id: number): Promise<Tag | null> {
  const { data, error } = await supabase
    .from('tags')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return null // Tag not found
    }
    console.error('Error fetching tag by ID:', error)
    throw new Error('Failed to fetch tag')
  }

  return data
}

// Create new tag
export async function createTag(tag: TagInsert): Promise<Tag> {
  const { data, error } = await supabaseAdmin
    .from('tags')
    .insert(tag)
    .select()
    .single()

  if (error) {
    console.error('Error creating tag:', error)
    throw new Error('Failed to create tag')
  }

  return data
}

// Update tag
export async function updateTag(id: number, tag: TagUpdate): Promise<Tag> {
  const { data, error } = await supabaseAdmin
    .from('tags')
    .update(tag)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating tag:', error)
    throw new Error('Failed to update tag')
  }

  return data
}

// Delete tag
export async function deleteTag(id: number): Promise<void> {
  const { error } = await supabaseAdmin
    .from('tags')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting tag:', error)
    throw new Error('Failed to delete tag')
  }
}

// Delete multiple tags
export async function deleteTags(ids: number[]): Promise<void> {
  const { error } = await supabaseAdmin
    .from('tags')
    .delete()
    .in('id', ids)

  if (error) {
    console.error('Error deleting tags:', error)
    throw new Error('Failed to delete tags')
  }
}

// Search tags by name
export async function searchTags(searchTerm: string): Promise<Tag[]> {
  const { data, error } = await supabase
    .from('tags')
    .select('*')
    .ilike('name', `%${searchTerm}%`)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error searching tags:', error)
    throw new Error('Failed to search tags')
  }

  return data
}

// Get tags with post count
export async function getTagsWithPostCount(): Promise<Array<Tag & { post_count: number }>> {
  const { data, error } = await supabase
    .from('tags')
    .select(`
      *,
      post_tags (count)
    `)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching tags with post count:', error)
    throw new Error('Failed to fetch tags with post count')
  }

  return data.map(tag => ({
    ...tag,
    post_count: tag.post_tags?.length || 0
  })) as Array<Tag & { post_count: number }>
}
