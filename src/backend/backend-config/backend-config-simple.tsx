'use client'

import { useState } from 'react'
import Field from '@/components/ui/field'
import KlButton from '@/components/ui/button'
import IconSelf from '@/components/icons/icon-self'
import { useToast } from '@/hooks'

export const BackendConfig = () => {
  const Toast = useToast()
  const [apiKey, setApiKey] = useState('')

  const handleSave = () => {
    Toast({ type: 'success', description: '配置保存成功！' })
  }

  return (
    <div className="h-[88vh] w-[95vw] flex flex-col">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-primary dark:text-darkprimary mb-2">
          系统配置
        </h1>
        <p className="text-secondary dark:text-darksecondary">
          配置 AI 总结功能的相关参数
        </p>
      </div>

      <div className="flex-1 bg-bgPrimary dark:bg-darkBgPrimary rounded-lg border border-borderColor dark:border-darkBorderColor p-6">
        <div className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-primary dark:text-darkprimary">
              API Key
            </label>
            <Field
              type="password"
              placeholder="请输入 API Key"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="w-full"
              inputWrapper_className="h-10"
            />
          </div>

          <div className="flex gap-4">
            <KlButton onPress={handleSave} className="flex items-center gap-2">
              <IconSelf iconName="icon-[lucide--save]" />
              <span>保存配置</span>
            </KlButton>
          </div>
        </div>
      </div>
    </div>
  )
}
