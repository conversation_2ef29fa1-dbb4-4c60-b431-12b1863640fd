'use client'

import { useState, useCallback, useEffect } from 'react'
import Field from '@/components/ui/field'
import { KlSelect } from '@/components/ui/select'
import KlButton from '@/components/ui/button'
import IconSelf from '@/components/icons/icon-self'
import { useToast } from '@/hooks'
import { AIConfig, AIConfigManager } from '@/utils/ai-config'

// AI 模型配置选项
const AI_MODEL_OPTIONS = [
  { id: 'openai', children: 'OpenAI GPT' },
  { id: 'claude', children: 'Anthropic Claude' },
  { id: 'gemini', children: 'Google Gemini' },
  { id: 'qwen', children: '阿里通义千问' },
  { id: 'baidu', children: '百度文心一言' },
  { id: 'custom', children: '自定义 API' }
]

export const BackendConfig = () => {
  const Toast = useToast()
  
  // AI 配置状态
  const [aiConfig, setAiConfig] = useState<AIConfig>({
    apiKey: '',
    modelType: 'openai',
    apiUrl: 'https://api.openai.com/v1',
    maxTokens: 2000,
    temperature: 0.7
  })
  const [isLoading, setIsLoading] = useState(false)

  // 组件挂载时加载配置
  useEffect(() => {
    const config = AIConfigManager.getConfig()
    setAiConfig(config)
  }, [])

  // 获取默认 API URL
  const getDefaultApiUrl = useCallback((modelType: string): string => {
    const defaultUrls: Record<string, string> = {
      openai: 'https://api.openai.com/v1',
      claude: 'https://api.anthropic.com/v1',
      gemini: 'https://generativelanguage.googleapis.com/v1',
      qwen: 'https://dashscope.aliyuncs.com/api/v1',
      baidu: 'https://aip.baidubce.com/rpc/2.0',
      custom: ''
    }
    return defaultUrls[modelType] || ''
  }, [])

  // 保存配置
  const handleSaveConfig = useCallback(async () => {
    setIsLoading(true)
    try {
      const validation = AIConfigManager.validateConfig(aiConfig)
      if (!validation.valid) {
        Toast({ type: 'error', description: `配置验证失败: ${validation.errors.join(', ')}` })
        return
      }

      const success = AIConfigManager.saveConfig(aiConfig)
      if (success) {
        Toast({ type: 'success', description: '配置保存成功！' })
      } else {
        Toast({ type: 'error', description: '配置保存失败，请重试！' })
      }
    } catch (error) {
      console.error('保存配置失败:', error)
      Toast({ type: 'error', description: '配置保存失败，请重试！' })
    } finally {
      setIsLoading(false)
    }
  }, [aiConfig, Toast])

  // 测试连接
  const handleTestConnection = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await AIConfigManager.testConnection(aiConfig)
      if (result.success) {
        Toast({ type: 'success', description: result.message })
      } else {
        Toast({ type: 'error', description: result.message })
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      Toast({ type: 'error', description: '连接测试失败，请检查配置！' })
    } finally {
      setIsLoading(false)
    }
  }, [aiConfig, Toast])

  // 重置配置
  const handleResetConfig = useCallback(() => {
    const defaultConfig = AIConfigManager.getConfig()
    setAiConfig(defaultConfig)
    AIConfigManager.clearConfig()
    Toast({ type: 'info', description: '配置已重置！' })
  }, [Toast])

  // 处理模型类型变化
  const handleModelTypeChange = useCallback((keys: any) => {
    const selectedKey = Array.from(keys)[0] as string
    if (selectedKey) {
      setAiConfig(prev => ({
        ...prev,
        modelType: selectedKey,
        apiUrl: getDefaultApiUrl(selectedKey)
      }))
    }
  }, [getDefaultApiUrl])

  return (
    <div className="h-[88vh] w-[95vw] flex flex-col">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-primary dark:text-darkprimary mb-2">
          系统配置
        </h1>
        <p className="text-secondary dark:text-darksecondary">
          配置 AI 总结功能的相关参数，包括 API 密钥和模型设置
        </p>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col lg:flex-row gap-6">
        {/* 左侧：模型信息 */}
        <div className="w-full lg:w-80 space-y-6">
          <div className="bg-bgPrimary dark:bg-darkBgPrimary rounded-lg border border-borderColor dark:border-darkBorderColor p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <IconSelf iconName="icon-[lucide--brain-circuit]" className="text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold text-primary dark:text-darkprimary">AI 模型</h3>
                <p className="text-sm text-secondary dark:text-darksecondary">
                  {AI_MODEL_OPTIONS.find(opt => opt.id === aiConfig.modelType)?.children || '未选择'}
                </p>
              </div>
            </div>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-secondary dark:text-darksecondary">状态</span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  aiConfig.apiKey ? 
                  'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400' : 
                  'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400'
                }`}>
                  {aiConfig.apiKey ? '已配置' : '未配置'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：配置表单 */}
        <div className="flex-1 bg-bgPrimary dark:bg-darkBgPrimary rounded-lg border border-borderColor dark:border-darkBorderColor p-6">
          <div className="space-y-6">
            <div className="border-b border-borderColor dark:border-darkBorderColor pb-4">
              <h3 className="text-lg font-semibold text-primary dark:text-darkprimary">AI 配置</h3>
              <p className="text-sm text-secondary dark:text-darksecondary mt-1">
                配置您的 AI 服务参数以启用智能功能
              </p>
            </div>

            {/* AI 模型选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-primary dark:text-darkprimary">
                AI 模型类型
              </label>
              <KlSelect
                selectProps={{
                  selectedKeys: [aiConfig.modelType],
                  onSelectionChange: handleModelTypeChange,
                  placeholder: '选择 AI 模型类型',
                  classNames: {
                    trigger: 'bg-bgPrimary dark:bg-darkBgPrimary border-borderColor dark:border-darkBorderColor h-10'
                  }
                }}
                selectItemProps={{
                  selectDatas: AI_MODEL_OPTIONS,
                  className: 'data-[selected=true]:!bg-activeColor dark:data-[selected=true]:!bg-darkActiveColor'
                }}
              />
            </div>

            {/* API Key */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-primary dark:text-darkprimary">
                API Key
              </label>
              <Field
                type="password"
                placeholder="请输入 API Key"
                value={aiConfig.apiKey}
                onChange={(e) => setAiConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                className="w-full"
                inputWrapper_className="h-10"
              />
            </div>

            {/* API URL */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-primary dark:text-darkprimary">
                API URL
              </label>
              <Field
                placeholder="请输入 API 地址"
                value={aiConfig.apiUrl}
                onChange={(e) => setAiConfig(prev => ({ ...prev, apiUrl: e.target.value }))}
                className="w-full"
                inputWrapper_className="h-10"
              />
            </div>

            {/* 操作按钮 */}
            <div className="pt-6 border-t border-borderColor dark:border-darkBorderColor">
              <div className="flex flex-wrap gap-3">
                <KlButton 
                  onPress={handleSaveConfig} 
                  className="flex items-center gap-2"
                  isLoading={isLoading}
                  isDisabled={isLoading}
                  fill={true}
                >
                  <IconSelf iconName="icon-[lucide--save]" />
                  <span>保存配置</span>
                </KlButton>
                
                <KlButton 
                  onPress={handleTestConnection} 
                  className="flex items-center gap-2"
                  isLoading={isLoading}
                  isDisabled={isLoading}
                >
                  <IconSelf iconName="icon-[lucide--wifi]" />
                  <span>测试连接</span>
                </KlButton>
                
                <KlButton 
                  onPress={handleResetConfig} 
                  className="flex items-center gap-2"
                  isDisabled={isLoading}
                >
                  <IconSelf iconName="icon-[lucide--rotate-ccw]" />
                  <span>重置配置</span>
                </KlButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
