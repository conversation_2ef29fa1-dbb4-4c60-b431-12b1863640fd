'use client'

import { useState, useCallback, useEffect } from 'react'
import Field from '@/components/ui/field'
import { KlSelect } from '@/components/ui/select'
import KlButton from '@/components/ui/button'
import IconSelf from '@/components/icons/icon-self'
import { useToast } from '@/hooks'
import { AIConfig, AIConfigManager } from '@/utils/ai-config'

// AI 模型配置选项
const AI_MODEL_OPTIONS = [
  { id: 'openai', children: 'OpenAI GPT' },
  { id: 'claude', children: 'Anthropic Claude' },
  { id: 'gemini', children: 'Google Gemini' },
  { id: 'qwen', children: '阿里通义千问' },
  { id: 'baidu', children: '百度文心一言' },
  { id: 'custom', children: '自定义 API' }
]

export const BackendConfig = () => {
  const Toast = useToast()

  // AI 配置状态
  const [aiConfig, setAiConfig] = useState<AIConfig>(AIConfigManager.getConfig())
  const [isLoading, setIsLoading] = useState(false)

  // 组件挂载时加载配置
  useEffect(() => {
    const config = AIConfigManager.getConfig()
    setAiConfig(config)
  }, [])

  // 保存配置
  const handleSaveConfig = useCallback(async () => {
    setIsLoading(true)
    try {
      // 验证配置
      const validation = AIConfigManager.validateConfig(aiConfig)
      if (!validation.valid) {
        Toast({ type: 'error', description: `配置验证失败: ${validation.errors.join(', ')}` })
        return
      }

      // 保存配置
      const success = AIConfigManager.saveConfig(aiConfig)
      if (success) {
        Toast({ type: 'success', description: '配置保存成功！' })
      } else {
        Toast({ type: 'error', description: '配置保存失败，请重试！' })
      }
    } catch (error) {
      console.error('保存配置失败:', error)
      Toast({ type: 'error', description: '配置保存失败，请重试！' })
    } finally {
      setIsLoading(false)
    }
  }, [aiConfig, Toast])

  // 测试连接
  const handleTestConnection = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await AIConfigManager.testConnection(aiConfig)
      if (result.success) {
        Toast({ type: 'success', description: result.message })
      } else {
        Toast({ type: 'error', description: result.message })
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      Toast({ type: 'error', description: '连接测试失败，请检查配置！' })
    } finally {
      setIsLoading(false)
    }
  }, [aiConfig, Toast])

  // 获取默认 API URL
  const getDefaultApiUrl = useCallback((modelType: string): string => {
    const defaultUrls: Record<string, string> = {
      openai: 'https://api.openai.com/v1',
      claude: 'https://api.anthropic.com/v1',
      gemini: 'https://generativelanguage.googleapis.com/v1',
      qwen: 'https://dashscope.aliyuncs.com/api/v1',
      baidu: 'https://aip.baidubce.com/rpc/2.0',
      custom: ''
    }
    return defaultUrls[modelType] || ''
  }, [])

  // 重置配置
  const handleResetConfig = useCallback(() => {
    const defaultConfig = AIConfigManager.getConfig()
    setAiConfig(defaultConfig)
    AIConfigManager.clearConfig()
    Toast({ type: 'info', description: '配置已重置！' })
  }, [Toast])

  // 处理模型类型变化
  const handleModelTypeChange = useCallback((keys: any) => {
    const selectedKey = Array.from(keys)[0] as string
    if (selectedKey) {
      setAiConfig(prev => ({
        ...prev,
        modelType: selectedKey,
        // 根据模型类型设置默认 API URL
        apiUrl: getDefaultApiUrl(selectedKey)
      }))
    }
  }, [getDefaultApiUrl])

  return (
    <div className="h-[88vh] w-[95vw] flex flex-col">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-primary dark:text-darkprimary mb-2">
          系统配置
        </h1>
        <p className="text-secondary dark:text-darksecondary">
          配置 AI 总结功能的相关参数，包括 API 密钥和模型设置
        </p>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col lg:flex-row gap-6">
        {/* 左侧：模型信息和功能列表 */}
        <div className="w-full lg:w-80 space-y-6">
          {/* AI 模型信息卡片 */}
          <div className="bg-bgPrimary dark:bg-darkBgPrimary rounded-lg border border-borderColor dark:border-darkBorderColor p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <IconSelf iconName="icon-[lucide--brain-circuit]" className="text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold text-primary dark:text-darkprimary">AI 模型</h3>
                <p className="text-sm text-secondary dark:text-darksecondary">
                  {AI_MODEL_OPTIONS.find(opt => opt.id === aiConfig.modelType)?.children || '未选择'}
                </p>
              </div>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-secondary dark:text-darksecondary">状态</span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  aiConfig.apiKey ?
                  'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400' :
                  'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400'
                }`}>
                  {aiConfig.apiKey ? '已配置' : '未配置'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary dark:text-darksecondary">Max Tokens</span>
                <span className="text-primary dark:text-darkprimary">{aiConfig.maxTokens}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary dark:text-darksecondary">Temperature</span>
                <span className="text-primary dark:text-darkprimary">{aiConfig.temperature}</span>
              </div>
            </div>
          </div>

          {/* 功能模块列表 */}
          <div className="bg-bgPrimary dark:bg-darkBgPrimary rounded-lg border border-borderColor dark:border-darkBorderColor p-6">
            <h3 className="font-semibold text-primary dark:text-darkprimary mb-4">功能模块</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center gap-3">
                  <IconSelf iconName="icon-[lucide--sparkles]" className="text-blue-600 dark:text-blue-400" />
                  <div>
                    <div className="font-medium text-blue-800 dark:text-blue-200">AI 总结</div>
                    <div className="text-xs text-blue-600 dark:text-blue-400">自动生成文章摘要</div>
                  </div>
                </div>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-950/20 border border-gray-200 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <IconSelf iconName="icon-[lucide--message-square]" className="text-gray-600 dark:text-gray-400" />
                  <div>
                    <div className="font-medium text-gray-800 dark:text-gray-200">智能问答</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">即将推出</div>
                  </div>
                </div>
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-950/20 border border-gray-200 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <IconSelf iconName="icon-[lucide--search]" className="text-gray-600 dark:text-gray-400" />
                  <div>
                    <div className="font-medium text-gray-800 dark:text-gray-200">语义搜索</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">即将推出</div>
                  </div>
                </div>
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：配置表单 */}
        <div className="flex-1 bg-bgPrimary dark:bg-darkBgPrimary rounded-lg border border-borderColor dark:border-darkBorderColor p-6">
          <div className="space-y-6">
            {/* 配置表单标题 */}
            <div className="border-b border-borderColor dark:border-darkBorderColor pb-4">
              <h3 className="text-lg font-semibold text-primary dark:text-darkprimary">AI 配置</h3>
              <p className="text-sm text-secondary dark:text-darksecondary mt-1">
                配置您的 AI 服务参数以启用智能功能
              </p>
            </div>

            {/* AI 模型选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-primary dark:text-darkprimary">
                AI 模型类型
              </label>
              <KlSelect
                selectProps={{
                  selectedKeys: [aiConfig.modelType],
                  onSelectionChange: handleModelTypeChange,
                  placeholder: '选择 AI 模型类型',
                  classNames: {
                    trigger: 'bg-bgPrimary dark:bg-darkBgPrimary border-borderColor dark:border-darkBorderColor h-10'
                  }
                }}
                selectItemProps={{
                  selectDatas: AI_MODEL_OPTIONS,
                  className: 'data-[selected=true]:!bg-activeColor dark:data-[selected=true]:!bg-darkActiveColor'
                }}
              />
            </div>

          {/* API Key */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-primary dark:text-darkprimary">
              API Key
            </label>
            <Field
              type="password"
              placeholder="请输入 API Key"
              value={aiConfig.apiKey}
              onChange={(e) => setAiConfig(prev => ({ ...prev, apiKey: e.target.value }))}
              className="w-full"
              inputWrapper_className="h-10"
            />
            <p className="text-xs text-secondary dark:text-darksecondary">
              请确保 API Key 具有相应的权限，用于调用 AI 服务
            </p>
          </div>

          {/* API URL */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-primary dark:text-darkprimary">
              API URL
            </label>
            <Field
              placeholder="请输入 API 地址"
              value={aiConfig.apiUrl}
              onChange={(e) => setAiConfig(prev => ({ ...prev, apiUrl: e.target.value }))}
              className="w-full"
              inputWrapper_className="h-10"
            />
            <p className="text-xs text-secondary dark:text-darksecondary">
              API 服务的基础地址，留空将使用默认地址
            </p>
          </div>

          {/* 高级设置 */}
          <div className="space-y-4">
            <div className="border-b border-borderColor dark:border-darkBorderColor pb-2">
              <h3 className="text-lg font-semibold text-primary dark:text-darkprimary">
                高级设置
              </h3>
              <p className="text-sm text-secondary dark:text-darksecondary mt-1">
                调整 AI 模型的行为参数
              </p>
            </div>

            {/* 参数设置网格布局 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Max Tokens */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-primary dark:text-darkprimary">
                  最大 Token 数量
                </label>
                <Field
                  type="number"
                  placeholder="2000"
                  value={aiConfig.maxTokens.toString()}
                  onChange={(e) => setAiConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) || 2000 }))}
                  className="w-full"
                  inputWrapper_className="h-10"
                />
                <p className="text-xs text-secondary dark:text-darksecondary">
                  建议值：1000-4000
                </p>
              </div>

              {/* Temperature */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-primary dark:text-darkprimary">
                  创造性参数
                </label>
                <Field
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  placeholder="0.7"
                  value={aiConfig.temperature.toString()}
                  onChange={(e) => setAiConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) || 0.7 }))}
                  className="w-full"
                  inputWrapper_className="h-10"
                />
                <p className="text-xs text-secondary dark:text-darksecondary">
                  范围：0-2，推荐 0.7
                </p>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="pt-6 border-t border-borderColor dark:border-darkBorderColor">
            <div className="flex flex-wrap gap-3">
              <KlButton
                onPress={handleSaveConfig}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                isLoading={isLoading}
                isDisabled={isLoading}
                fill={true}
              >
                <IconSelf iconName="icon-[lucide--save]" />
                <span>保存配置</span>
              </KlButton>

              <KlButton
                onPress={handleTestConnection}
                className="flex items-center gap-2"
                isLoading={isLoading}
                isDisabled={isLoading}
              >
                <IconSelf iconName="icon-[lucide--wifi]" />
                <span>测试连接</span>
              </KlButton>

              <KlButton
                onPress={handleResetConfig}
                className="flex items-center gap-2"
                isDisabled={isLoading}
              >
                <IconSelf iconName="icon-[lucide--rotate-ccw]" />
                <span>重置配置</span>
              </KlButton>
            </div>

            {/* 提示信息 */}
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-start gap-2">
                <IconSelf iconName="icon-[lucide--info]" className="text-blue-600 dark:text-blue-400 mt-0.5 shrink-0" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">配置说明</p>
                  <ul className="text-xs space-y-1 text-blue-700 dark:text-blue-300">
                    <li>• 保存配置后将自动应用到 AI 总结功能</li>
                    <li>• 建议先测试连接确保配置正确</li>
                    <li>• API Key 将安全存储在本地浏览器中</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
