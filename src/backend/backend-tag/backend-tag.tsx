'use client'

import { TagTable, TagTableHandle } from '@/backend/backend-tag/tag-table'
import IconSelf from '@/components/icons/icon-self'
import KlButton from '@/components/ui/button'
import Field from '@/components/ui/field'
import KlModal from '@/components/ui/modal'
import { TagFormModal } from '@/components/tag-form-modal'
import { useToast } from '@/hooks'
import { TableRowsToArray } from '@/utils'
import { useCallback, useRef, useState } from 'react'

export const BackendTag = () => {
  const Toast = useToast()
  // 提示框状态
  const [open, setOpen] = useState(false)
  // 创建标签模态框状态
  const [openCreateTag, setOpenCreateTag] = useState(false)
  // 编辑标签ID
  const [editTagId, setEditTagId] = useState<number | null>(null)
  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState('')
  // 标签表格实例
  const TagTableRef = useRef<TagTableHandle>(null)

  // 处理表格删除事件
  const ModalHandler = useCallback(async () => {
    if (TagTableRef.current) {
      const selectedKeys = TableRowsToArray(
        TagTableRef.current.selectedKeys,
        TagTableRef.current.allRowKeys
      )

      try {
        const response = await fetch('/api/tags', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ ids: selectedKeys })
        })
        const result = await response.json()

        if (result.success) {
          Toast({ type: 'success', description: '删除成功！' })
          TagTableRef.current?.refreshData()
        } else {
          Toast({ type: 'error', description: result.error || '删除失败' })
        }
      } catch (error) {
        console.error('Error deleting tags:', error)
        Toast({ type: 'error', description: '删除失败' })
      }
    }
  }, [Toast])

  // 点击删除标签按钮时候校验
  const delTag = useCallback(() => {
    if (TagTableRef.current) {
      const selectedKeys = TableRowsToArray(
        TagTableRef.current.selectedKeys,
        TagTableRef.current.allRowKeys
      )
      if (selectedKeys.length > 0) {
        setOpen(true)
      } else {
        Toast({ type: 'warning', description: '请选择要删除的标签！' })
      }
    }
  }, [Toast])

  // 处理标签表单成功回调
  const handleTagFormSuccess = useCallback(() => {
    TagTableRef.current?.refreshData()
  }, [])

  // 处理搜索
  const handleSearch = useCallback(() => {
    // TODO: 实现搜索功能
    Toast({ type: 'info', description: '搜索功能开发中...' })
  }, [Toast])

  // 处理编辑标签
  const handleEditTag = useCallback((tagId: number) => {
    setEditTagId(tagId)
    setOpenCreateTag(true)
  }, [])

  // 关闭标签表单模态框
  const handleCloseTagForm = useCallback((open: boolean) => {
    setOpenCreateTag(open)
    if (!open) {
      setEditTagId(null)
    }
  }, [])

  return (
    <div className="h-[88vh] w-[95vw] flex flex-col">
      {/* 搜索栏 */}
      <div className="flex items-center justify-between gap-6">
        {/* 名称 */}
        <Field
          className="w-80"
          placeholder="请输入标签名称"
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
        />

        <div className="flex gap-6">
          {/* 搜索按钮 */}
          <KlButton fill={true} onPress={handleSearch}>
            <div className="flex items-center gap-2">
              <IconSelf iconName="icon-[lucide--search]" />
              <span>搜索</span>
            </div>
          </KlButton>
          {/* 创建标签按钮 */}
          <KlButton fill={true} onPress={() => setOpenCreateTag(true)}>
            <div className="flex items-center gap-2">
              <IconSelf iconName="icon-[lucide--plus]" />
              <span>创建标签</span>
            </div>
          </KlButton>
          {/* 删除按钮 */}
          <KlButton fill={true} onPress={() => delTag()}>
            <div className="flex items-center gap-2">
              <IconSelf iconName="icon-[lucide--trash]" />
              <span>删除</span>
            </div>
          </KlButton>
        </div>
      </div>

      {/* 表格 */}
      <TagTable ref={TagTableRef} onEdit={handleEditTag} />

      {/* modal提示框 */}
      <KlModal
        content="确定删除选中的多条数据吗？"
        open={open}
        setOpen={setOpen}
        successCallback={() => ModalHandler()}
      />

      {/* 创建/编辑标签模态框 */}
      <TagFormModal
        open={openCreateTag}
        setOpen={handleCloseTagForm}
        tagId={editTagId}
        onSuccess={handleTagFormSuccess}
      />
    </div>
  )
}
