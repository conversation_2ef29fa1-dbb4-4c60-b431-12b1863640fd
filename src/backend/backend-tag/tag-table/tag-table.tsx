'use client'

import React, { forwardRef, useCallback, useImperativeHandle, useEffect, useState } from 'react'
import { Selection } from '@heroui/react'
import KlButton from '@/components/ui/button'
import IconSelf from '@/components/icons/icon-self'
import { clm } from '@/utils'
import {
  KlTable,
  KlTableBody,
  KlTableCell,
  KlTableColumn,
  KlTableHeader,
  KlTableRow
} from '@/components/ui/table'
import { KlPagination } from '@/components/ui/pagination'
import { PerPage } from '@/components/ui/per-page'
import KlModal from '@/components/ui/modal'
import { useToast } from '@/hooks'

export const columns = [
  { children: 'ID', uid: 'id' },
  {
    uid: 'name',
    children: (
      <div className="flex items-center gap-1 text-[14px]">
        <IconSelf iconName="icon-[lucide--type]" />
        <div>名称</div>
      </div>
    )
  },
  {
    uid: 'light_icon',
    children: (
      <div className="flex items-center gap-1 text-[14px]">
        <IconSelf iconName="icon-[lucide--image]" />
        <div>浅色标签</div>
      </div>
    )
  },
  {
    uid: 'dark_icon',
    children: (
      <div className="flex items-center gap-1 text-[14px]  font-semibold">
        <IconSelf iconName="icon-[lucide--image]" />
        <div>深色标签</div>
      </div>
    )
  },
  {
    uid: 'created_at',
    children: (
      <KlButton
        className={clm(
          'gap-1 text-[14px] border-0 h-8 font-semibold',
          'bg-darkBgPrimary text-darkprimary dark:bg-bgPrimary dark:text-primary hover:bg-transparent hover:dark:bg-hoverColor'
        )}
      >
        <IconSelf iconName="icon-[lucide--calendar]" />
        <div>创建时间</div>
      </KlButton>
    )
  },
  {
    uid: 'updated_at',
    children: (
      <KlButton
        className={clm(
          'gap-1 text-[14px] border-0 h-8 font-semibold',
          'bg-darkBgPrimary text-darkprimary dark:bg-bgPrimary dark:text-primary hover:bg-transparent hover:dark:bg-hoverColor'
        )}
      >
        <IconSelf iconName="icon-[lucide--calendar]" />
        <div>更新时间</div>
      </KlButton>
    )
  },
  { children: '', uid: 'actions' }
]

// 标签数据类型
interface TagData {
  id: number
  name: string
  light_icon: string | null
  dark_icon: string | null
  created_at: string
  updated_at: string
}

const INITIAL_VISIBLE_COLUMNS = [
  'name',
  'light_icon',
  'dark_icon',
  'created_at',
  'updated_at',
  'actions'
]

export interface TagTableHandle {
  selectedKeys: 'all' | Iterable<React.Key> | undefined
  allRowKeys: number[]
  refreshData: () => void
}

interface TagTableProps {
  onEdit?: (tagId: number) => void
}

export const TagTable = forwardRef<TagTableHandle, TagTableProps>(({ onEdit }, ref) => {
  const Toast = useToast()
  // 标签数据
  const [tags, setTags] = useState<TagData[]>([])
  const [loading, setLoading] = useState(true)
  // 表格行选择的keys
  const [selectedKeys, setSelectedKeys] = React.useState<Selection>(new Set([]))
  // 实际显示的行表头属性值
  const [visibleColumns] = React.useState<Selection>(new Set(INITIAL_VISIBLE_COLUMNS))
  // 获取当前点击actions时表格的key
  const [currentID, setCurrentID] = React.useState<number | null>(null)
  // 提示框状态
  const [open, setOpen] = React.useState(false)
  // 表格每页的行数
  const [rowsPerPage, setRowsPerPage] = React.useState(10)
  // 表格当前页码
  const [page, setPage] = React.useState(1)
  // 表格总页数
  const pages = Math.ceil(tags.length / rowsPerPage) || 1

  // 获取标签数据
  const fetchTags = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/tags')
      const result = await response.json()

      if (result.success) {
        setTags(result.data)
      } else {
        Toast({ type: 'error', description: '获取标签失败' })
      }
    } catch (error) {
      console.error('Error fetching tags:', error)
      Toast({ type: 'error', description: '获取标签失败' })
    } finally {
      setLoading(false)
    }
  }, [Toast])

  // 组件挂载时获取数据
  useEffect(() => {
    fetchTags()
  }, [fetchTags])

  // 暴露给父组件的变量和方法
  useImperativeHandle(ref, () => ({
    selectedKeys,
    allRowKeys: tags.map((row) => row.id),
    refreshData: fetchTags
  }))

  // 处理表格删除事件
  const ModalHandler = useCallback(async () => {
    if (!currentID) return

    try {
      const response = await fetch(`/api/tags/${currentID}`, {
        method: 'DELETE'
      })
      const result = await response.json()

      if (result.success) {
        Toast({ type: 'success', description: '删除成功！' })
        fetchTags() // 刷新数据
      } else {
        Toast({ type: 'error', description: result.error || '删除失败' })
      }
    } catch (error) {
      console.error('Error deleting tag:', error)
      Toast({ type: 'error', description: '删除失败' })
    }
  }, [currentID, Toast, fetchTags])

  // 处理后的表头（过滤掉不相交的表头属性数据）
  const headerColumns = React.useMemo(() => {
    if (visibleColumns === 'all') return columns

    return columns.filter((column) => Array.from(visibleColumns).includes(column.uid))
  }, [visibleColumns])

  // 处理后的表格行数据
  const items = React.useMemo(() => {
    const start = (page - 1) * rowsPerPage
    const end = start + rowsPerPage

    return tags.slice(start, end)
  }, [page, rowsPerPage, tags])

  // 格式化日期
  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }, [])

  // 表格行单元格的渲染设置方法
  const renderCell = React.useCallback((tag: TagData, columnKey: React.Key) => {
    const cellValue = tag[columnKey as keyof TagData]

    switch (columnKey) {
      case 'created_at':
      case 'updated_at':
        return (
          <div className="flex flex-col">
            <p className="text-bold text-small">{formatDate(cellValue as string)}</p>
          </div>
        )
      case 'light_icon':
      case 'dark_icon':
        return (
          <div className="flex flex-col">
            <p className="text-bold text-small">{cellValue || 'N/A'}</p>
          </div>
        )
      case 'actions':
        return (
          <div className="relative flex justify-end items-center gap-2">
            <KlButton
              isIconOnly={true}
              onPress={() => onEdit?.(tag.id)}
            >
              <IconSelf iconName="icon-[lucide--edit-2]" />
            </KlButton>
            <KlButton
              isIconOnly={true}
              onPress={() => {
                setOpen(true)
                setCurrentID(tag.id)
              }}
            >
              <IconSelf iconName="icon-[lucide--trash]" className="text-[#EF4444]" />
            </KlButton>
          </div>
        )
      default:
        return (
          <div className="flex flex-col">
            <p className="text-bold text-small capitalize">{cellValue}</p>
          </div>
        )
    }
  }, [formatDate])

  // 分页器条数设置方法
  const onRowsPerPageChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setRowsPerPage(Number(e.target.value))
    setPage(1)
  }, [])

  // 表格底部组件
  const tableBottomContent = React.useMemo(() => {
    return (
      <div className="py-2 px-2 flex justify-between items-center">
        <span className="w-[30%] text-small text-secondary dark:text-darksecondary">
          {selectedKeys === 'all'
            ? `已全选，总共 ${tags.length} 项`
            : `已选择 ${selectedKeys.size} 项，总共 ${tags.length} 项`}
        </span>
        <div className="flex gap-10 justify-end items-center min-w-100">
          <KlPagination page={page} total={pages} onChange={setPage} />
          <PerPage defaultSelectedKeys={'10'} onChange={onRowsPerPageChange} />
        </div>
      </div>
    )
  }, [selectedKeys, onRowsPerPageChange, page, pages, tags.length])

  return (
    <>
      {/* modal提示框 */}
      <KlModal
        content="确定删除该条数据吗？"
        open={open}
        setOpen={setOpen}
        successCallback={() => ModalHandler()}
      />
      {/* 表格 */}
      <KlTable
        bottomContent={tableBottomContent}
        selectedKeys={selectedKeys}
        onSelectionChange={setSelectedKeys}
      >
        <KlTableHeader columns={headerColumns}>
          {(column) => (
            <KlTableColumn key={column.uid} align={column.uid === 'actions' ? 'center' : 'start'}>
              {column.children}
            </KlTableColumn>
          )}
        </KlTableHeader>
        <KlTableBody
          emptyContent={loading ? '加载中...' : '暂无标签数据'}
          items={items}
          isLoading={loading}
        >
          {(item) => (
            <KlTableRow key={item.id}>
              {(columnKey) => <KlTableCell>{renderCell(item, columnKey)}</KlTableCell>}
            </KlTableRow>
          )}
        </KlTableBody>
      </KlTable>
    </>
  )
})

TagTable.displayName = 'TagTable'
