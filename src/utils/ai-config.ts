// AI 配置接口
export interface AIConfig {
  apiKey: string
  modelType: string
  apiUrl: string
  maxTokens: number
  temperature: number
}

// 默认配置
export const DEFAULT_AI_CONFIG: AIConfig = {
  apiKey: '',
  modelType: 'openai',
  apiUrl: 'https://api.openai.com/v1',
  maxTokens: 2000,
  temperature: 0.7
}

// 本地存储键名
const AI_CONFIG_STORAGE_KEY = 'ai_config'

// AI 配置管理类
export class AIConfigManager {
  // 获取配置
  static getConfig(): AIConfig {
    if (typeof window === 'undefined') {
      return DEFAULT_AI_CONFIG
    }

    try {
      const stored = localStorage.getItem(AI_CONFIG_STORAGE_KEY)
      if (stored) {
        const config = JSON.parse(stored)
        return { ...DEFAULT_AI_CONFIG, ...config }
      }
    } catch (error) {
      console.error('获取 AI 配置失败:', error)
    }
    
    return DEFAULT_AI_CONFIG
  }

  // 保存配置
  static saveConfig(config: AIConfig): boolean {
    if (typeof window === 'undefined') {
      return false
    }

    try {
      localStorage.setItem(AI_CONFIG_STORAGE_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存 AI 配置失败:', error)
      return false
    }
  }

  // 清除配置
  static clearConfig(): boolean {
    if (typeof window === 'undefined') {
      return false
    }

    try {
      localStorage.removeItem(AI_CONFIG_STORAGE_KEY)
      return true
    } catch (error) {
      console.error('清除 AI 配置失败:', error)
      return false
    }
  }

  // 验证配置
  static validateConfig(config: AIConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.apiKey.trim()) {
      errors.push('API Key 不能为空')
    }

    if (!config.modelType.trim()) {
      errors.push('模型类型不能为空')
    }

    if (!config.apiUrl.trim()) {
      errors.push('API URL 不能为空')
    } else {
      try {
        new URL(config.apiUrl)
      } catch {
        errors.push('API URL 格式不正确')
      }
    }

    if (config.maxTokens <= 0 || config.maxTokens > 10000) {
      errors.push('最大 Token 数量应在 1-10000 之间')
    }

    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('Temperature 应在 0-2 之间')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 测试连接
  static async testConnection(config: AIConfig): Promise<{ success: boolean; message: string }> {
    const validation = this.validateConfig(config)
    if (!validation.valid) {
      return {
        success: false,
        message: `配置验证失败: ${validation.errors.join(', ')}`
      }
    }

    try {
      // 这里可以实现实际的 API 测试逻辑
      // 目前返回模拟结果
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络请求
      
      return {
        success: true,
        message: 'AI 服务连接测试成功'
      }
    } catch (error) {
      return {
        success: false,
        message: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }
}

// AI 总结服务
export class AISummaryService {
  private config: AIConfig

  constructor(config?: AIConfig) {
    this.config = config || AIConfigManager.getConfig()
  }

  // 更新配置
  updateConfig(config: AIConfig) {
    this.config = config
  }

  // 生成文章总结
  async generateSummary(content: string): Promise<{ success: boolean; summary?: string; error?: string }> {
    const validation = AIConfigManager.validateConfig(this.config)
    if (!validation.valid) {
      return {
        success: false,
        error: `配置无效: ${validation.errors.join(', ')}`
      }
    }

    try {
      // 这里实现实际的 AI API 调用
      // 目前返回模拟结果
      const mockSummary = `本文简单介绍了${content.slice(0, 20)}...的相关内容，包含了实用的技术要点和实现方法。`
      
      await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟 API 调用延迟
      
      return {
        success: true,
        summary: mockSummary
      }
    } catch (error) {
      return {
        success: false,
        error: `生成总结失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }
}

// 导出单例实例
export const aiSummaryService = new AISummaryService()
