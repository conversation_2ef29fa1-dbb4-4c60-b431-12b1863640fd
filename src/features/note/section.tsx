'use client'

import { useState, useMemo, useEffect } from 'react'
import { Card<PERSON><PERSON>, NoteCard } from '@/components/note-card'
import { FilterBar, SortOption } from './filter-bar'
import { PostWithTags } from '@/lib/database/posts'

export default function Section() {
  const [posts, setPosts] = useState<PostWithTags[]>([])
  const [loading, setLoading] = useState(true)
  const [searchKeyword, setSearchKeyword] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<SortOption>('newest')

  // 获取文章数据
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/posts')
        const result = await response.json()

        if (result.success) {
          setPosts(result.data)
        } else {
          console.error('Failed to fetch posts:', result.error)
        }
      } catch (error) {
        console.error('Error fetching posts:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [])

  // 筛选和排序逻辑
  const filteredAndSortedArticles = useMemo(() => {
    let filtered = posts

    // 关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase()
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(keyword) ||
        post.description?.toLowerCase().includes(keyword) ||
        post.content.toLowerCase().includes(keyword)
      )
    }

    // 标签筛选
    if (selectedTags.length > 0) {
      filtered = filtered.filter(post =>
        selectedTags.some(tag => post.tags.some(postTag => postTag.name === tag))
      )
    }

    // 排序
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        case 'title-asc':
          return a.title.localeCompare(b.title, 'zh-CN')
        case 'title-desc':
          return b.title.localeCompare(a.title, 'zh-CN')
        default:
          return 0
      }
    })

    return sorted
  }, [posts, searchKeyword, selectedTags, sortBy])

  // 重置所有筛选
  const handleReset = () => {
    setSearchKeyword('')
    setSelectedTags([])
    setSortBy('newest')
  }

  return (
    <div>
      {/* 标题 */}
      <h1 className="text-4xl max-md:text-3xl font-black mb-8">文章</h1>

      {/* 筛选栏 */}
      <FilterBar
        searchKeyword={searchKeyword}
        selectedTags={selectedTags}
        sortBy={sortBy}
        onSearchChange={setSearchKeyword}
        onTagsChange={setSelectedTags}
        onSortChange={setSortBy}
        onReset={handleReset}
      />

      {/* 结果统计 */}
      <div className="mb-6 text-secondary dark:text-darksecondary animate-fade-up animate-ease-in-out animate-delay-[200ms]">
        {loading ? (
          <span>加载中...</span>
        ) : filteredAndSortedArticles.length === posts.length ? (
          <span>共 {posts.length} 篇文章</span>
        ) : (
          <span>
            找到 {filteredAndSortedArticles.length} 篇文章，共 {posts.length} 篇
          </span>
        )}
      </div>

      {/* 文章列表 */}
      {filteredAndSortedArticles.length > 0 ? (
        <CardList>
          {filteredAndSortedArticles.map((article, index) => (
            <NoteCard
              key={article.id}
              article={article}
              className={`animate-fade-up animate-ease-in-out animate-delay-[${(index % 4) * 200 + 400}ms]`}
            />
          ))}
        </CardList>
      ) : (
        <div className="text-center py-16 animate-fade-up animate-ease-in-out animate-delay-[400ms]">
          <div className="text-6xl mb-4">📝</div>
          <h3 className="text-xl font-bold text-primary dark:text-darkprimary mb-2">
            没有找到相关文章
          </h3>
          <p className="text-secondary dark:text-darksecondary mb-4">
            尝试调整搜索关键词或筛选条件
          </p>
          <button
            onClick={handleReset}
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            重置筛选条件
          </button>
        </div>
      )}
    </div>
  )
}
