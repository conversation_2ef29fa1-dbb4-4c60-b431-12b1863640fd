'use client'

import { useState, useEffect } from 'react'
import KlModal from '@/components/ui/modal'
import Field from '@/components/ui/field'
import { useToast } from '@/hooks'

interface TagFormData {
  name: string
  light_icon: string
  dark_icon: string
}

interface TagFormModalProps {
  open: boolean
  setOpen: (open: boolean) => void
  tagId?: number | null
  onSuccess?: () => void
}

export const TagFormModal = ({ open, setOpen, tagId, onSuccess }: TagFormModalProps) => {
  const Toast = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<TagFormData>({
    name: '',
    light_icon: '',
    dark_icon: ''
  })

  const isEdit = !!tagId

  // 获取标签数据（编辑模式）
  useEffect(() => {
    if (isEdit && tagId && open) {
      fetchTagData()
    } else if (!isEdit && open) {
      // 新建模式，重置表单
      setFormData({
        name: '',
        light_icon: '',
        dark_icon: ''
      })
    }
  }, [isEdit, tagId, open])

  const fetchTagData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/tags/${tagId}`)
      const result = await response.json()
      
      if (result.success) {
        setFormData({
          name: result.data.name,
          light_icon: result.data.light_icon || '',
          dark_icon: result.data.dark_icon || ''
        })
      } else {
        Toast({ type: 'error', description: '获取标签数据失败' })
      }
    } catch (error) {
      console.error('Error fetching tag:', error)
      Toast({ type: 'error', description: '获取标签数据失败' })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      Toast({ type: 'error', description: '标签名称不能为空' })
      return
    }

    try {
      setLoading(true)
      const url = isEdit ? `/api/tags/${tagId}` : '/api/tags'
      const method = isEdit ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          light_icon: formData.light_icon.trim() || null,
          dark_icon: formData.dark_icon.trim() || null
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        Toast({ 
          type: 'success', 
          description: isEdit ? '标签更新成功！' : '标签创建成功！' 
        })
        setOpen(false)
        onSuccess?.()
      } else {
        Toast({ type: 'error', description: result.error || '操作失败' })
      }
    } catch (error) {
      console.error('Error saving tag:', error)
      Toast({ type: 'error', description: '操作失败' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof TagFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <KlModal
      open={open}
      setOpen={setOpen}
      title={isEdit ? '编辑标签' : '创建标签'}
      size="2xl"
      confirmName={isEdit ? '更新' : '创建'}
      successCallback={handleSubmit}
      loading={loading}
      content={
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-primary dark:text-darkprimary mb-2">
              标签名称 *
            </label>
            <Field
              placeholder="请输入标签名称"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-primary dark:text-darkprimary mb-2">
              浅色图标
            </label>
            <Field
              placeholder="请输入浅色图标路径或URL"
              value={formData.light_icon}
              onChange={(e) => handleInputChange('light_icon', e.target.value)}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-primary dark:text-darkprimary mb-2">
              深色图标
            </label>
            <Field
              placeholder="请输入深色图标路径或URL"
              value={formData.dark_icon}
              onChange={(e) => handleInputChange('dark_icon', e.target.value)}
              className="w-full"
            />
          </div>
        </div>
      }
    />
  )
}
