'use client'

import { clm } from '@/utils'
import IconSelf from '../icons/icon-self'
import { SKILL_ICON_REACT, SKILL_ICON_VUE } from '@/constants/info'
import { KlChip } from '@/components/ui/chip'
import Link from 'next/link'
import { PostWithTags } from '@/lib/database/posts'

interface INoteCardProps {
  article?: PostWithTags
  className?: string
}

export const NoteCard = ({ article, className }: INoteCardProps) => {
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    sessionStorage.setItem('modalRect', JSON.stringify(rect))
    sessionStorage.setItem('scrollY', window.scrollY.toString())
  }

  // 如果没有传入文章数据，显示默认内容
  if (!article) {
    return (
      <Link
        href="/note/test"
        className={clm(
          'flex flex-col px-6 py-4 rounded-xl',
          'hover:bg-lighterBgPrimary dark:hover:bg-darkerBgPrimary active:bg-lighterBgPrimary dark:active:bg-darkerBgPrimary hover:cursor-pointer',
          className
        )}
        onClick={(e) => handleClick(e)}
      >
        {/* 默认内容保持不变 */}
        <div className="hidden max-md:flex items-center flex-wrap gap-4 mb-2 text-xs text-content dark:text-darkContent">
          <div className="flex items-center gap-1">
            <span>#React</span>
            <IconSelf iconName={SKILL_ICON_REACT.iconPath} />
          </div>
          <div className="flex items-center gap-1">
            <span>#Vue</span>
            <IconSelf iconName={SKILL_ICON_VUE.iconPath} />
          </div>
          <div className="flex items-center gap-1">
            <span>#效率</span>
          </div>
        </div>
        <div className="text-xl font-black">这是一个笔记标题</div>
        <div className="text-sm text-content dark:text-darkContent my-4">这是一个笔记内容</div>
        <div className="text-xs text-content dark:text-darkContent flex flex-wrap items-center justify-between gap-2">
          <div className="flex max-md:hidden items-center flex-wrap gap-4 text-xs text-content dark:text-darkContent">
            <div className="flex items-center gap-1">
              <span>#React</span>
              <IconSelf iconName={SKILL_ICON_REACT.iconPath} />
            </div>
            <div className="flex items-center gap-1">
              <span>#Vue</span>
              <IconSelf iconName={SKILL_ICON_VUE.iconPath} />
            </div>
            <div className="flex items-center gap-1">
              <span>#效率</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <IconSelf iconName="icon-[lucide--calendar]" size="text-md" />
            <span>5月25，2025</span>
          </div>
        </div>
      </Link>
    )
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Link
      href={`/note/${article.slug}`}
      className={clm(
        'flex flex-col px-6 py-4 rounded-xl transition-all duration-200',
        'hover:bg-lighterBgPrimary dark:hover:bg-darkerBgPrimary active:bg-lighterBgPrimary dark:active:bg-darkerBgPrimary hover:cursor-pointer',
        'hover:scale-[1.02] hover:shadow-lg',
        className
      )}
      onClick={(e) => handleClick(e)}
    >
      {/* 移动端标签 */}
      <div className="hidden max-md:flex items-center flex-wrap gap-2 mb-3">
        {article.tags.slice(0, 3).map((tag) => (
          <KlChip key={tag.id} size="sm">
            #{tag.name}
          </KlChip>
        ))}
        {article.tags.length > 3 && (
          <span className="text-xs text-secondary dark:text-darksecondary">
            +{article.tags.length - 3}
          </span>
        )}
      </div>

      {/* 文章标题 */}
      <h3 className="text-xl font-black text-primary dark:text-darkprimary mb-3 line-clamp-2">
        {article.title}
      </h3>

      {/* 文章描述 */}
      <p className="text-sm text-content dark:text-darkContent mb-4 line-clamp-3 flex-1">
        {article.description}
      </p>

      {/* 文章信息 */}
      <div className="flex flex-wrap items-center justify-between gap-2 text-xs text-content dark:text-darkContent">
        {/* 桌面端标签 */}
        <div className="flex max-md:hidden items-center flex-wrap gap-2">
          {article.tags.slice(0, 3).map((tag) => (
            <KlChip key={tag.id} size="sm">
              #{tag.name}
            </KlChip>
          ))}
          {article.tags.length > 3 && (
            <span className="text-xs text-secondary dark:text-darksecondary">
              +{article.tags.length - 3}
            </span>
          )}
        </div>

        {/* 发布信息 */}
        <div className="flex items-center gap-4 text-xs">
          <div className="flex items-center gap-1">
            <IconSelf iconName="icon-[lucide--calendar]" className="text-sm" />
            <span>{formatDate(article.created_at)}</span>
          </div>
          <div className="flex items-center gap-1">
            <IconSelf iconName="icon-[lucide--clock]" className="text-sm" />
            <span>{article.read_time || 0} 分钟</span>
          </div>
        </div>
      </div>
    </Link>
  )
}
