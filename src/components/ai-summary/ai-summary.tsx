'use client'

import { useState, useCallback } from 'react'
import K<PERSON><PERSON>utton from '@/components/ui/button'
import IconSelf from '@/components/icons/icon-self'
import { useToast } from '@/hooks'

interface AISummaryProps {
  content: string
  className?: string
}

export const AISummary = ({ content, className = '' }: AISummaryProps) => {
  const Toast = useToast()
  const [summary, setSummary] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerated, setIsGenerated] = useState(false)

  // 生成 AI 总结
  const handleGenerateSummary = useCallback(async () => {
    if (!content.trim()) {
      Toast({ type: 'warning', description: '内容为空，无法生成总结' })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/ai-summary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content }),
      })

      const result = await response.json()

      if (result.success) {
        setSummary(result.summary)
        setIsGenerated(true)
        Toast({ type: 'success', description: 'AI 总结生成成功！' })
      } else {
        Toast({ type: 'error', description: result.error || 'AI 总结生成失败' })
      }
    } catch (error) {
      console.error('生成 AI 总结失败:', error)
      Toast({ type: 'error', description: '网络错误，请重试' })
    } finally {
      setIsLoading(false)
    }
  }, [content, Toast])

  // 重新生成总结
  const handleRegenerate = useCallback(() => {
    setSummary('')
    setIsGenerated(false)
    handleGenerateSummary()
  }, [handleGenerateSummary])

  return (
    <div className={`bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-center gap-2 mb-3">
        <IconSelf iconName="icon-[lucide--sparkles]" className="text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">AI 总结</h3>
      </div>

      {!isGenerated ? (
        <div className="text-center py-4">
          <p className="text-secondary dark:text-darksecondary mb-4">
            点击下方按钮，让 AI 为您生成文章总结
          </p>
          <KlButton
            onPress={handleGenerateSummary}
            isLoading={isLoading}
            isDisabled={isLoading}
            className="flex items-center gap-2"
          >
            <IconSelf iconName="icon-[lucide--wand-2]" />
            <span>{isLoading ? '正在生成...' : '生成 AI 总结'}</span>
          </KlButton>
        </div>
      ) : (
        <div>
          <div className="bg-white dark:bg-gray-800 rounded-md p-4 mb-4 border border-blue-100 dark:border-blue-900">
            <p className="text-primary dark:text-darkprimary leading-relaxed">
              {summary}
            </p>
          </div>
          <div className="flex gap-2">
            <KlButton
              size="sm"
              onPress={handleRegenerate}
              isLoading={isLoading}
              isDisabled={isLoading}
              className="flex items-center gap-1"
            >
              <IconSelf iconName="icon-[lucide--refresh-cw]" className="text-xs" />
              <span>重新生成</span>
            </KlButton>
          </div>
        </div>
      )}
    </div>
  )
}
